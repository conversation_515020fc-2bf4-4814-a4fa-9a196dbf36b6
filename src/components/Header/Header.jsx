import { Code2 } from "lucide-react"

import ColorPicker from "../ColorPicker/ColorPicker"

const Header = () => {
  return (
    // <motion.header
    //   initial={{ y: -100, opacity: 0 }}
    //   animate={{ y: 0, opacity: 1 }}
    //   transition={{ duration: 0.6, ease: "easeOut" }}
    //   className="fixed inset-x-0 top-0 z-50 border-b border-white/10 bg-black/20 backdrop-blur-md"
    // >
    <header className="fixed inset-x-0 top-0 z-50 border-b border-white/10 bg-black/20 backdrop-blur-md">
      <div className="container mx-auto px-8 py-4">
        <div className="flex items-center justify-between">
          {/* <motion.div
            className="flex items-center gap-2"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          > */}
          <div className="z-10 flex items-center gap-2">
            <Code2 className="h-6 w-6 text-white" />
            <h1 className="text-lg font-bold text-white md:text-md">
              <PERSON><PERSON><PERSON>
            </h1>
          </div>
          {/* </motion.div> */}

          {/* Color Picker */}
          <ColorPicker />
        </div>
      </div>
    </header>
    // </motion.header>
  )
}

export default Header
