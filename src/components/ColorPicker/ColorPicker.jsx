import { useState, useEffect } from "react"
import { Palette } from "lucide-react"

import { ColorSwatchGrid } from "@/components/ui/color-swatch"
import {
  BRIGHT_COLORS,
  getSavedColor,
  saveColorPreference,
  applyColorToCSSVariables
} from "@/lib/colors"
import { cn } from "@/lib/utils"

const ColorPicker = ({ className }) => {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedColor, setSelectedColor] = useState(getSavedColor())

  useEffect(() => {
    // Initialize with saved color on mount
    const savedColor = getSavedColor()
    setSelectedColor(savedColor)
    applyColorToCSSVariables(savedColor)
  }, [])

  const handleColorSelect = (color) => {
    setSelectedColor(color.value)
    saveColorPreference(color.value)
    applyColorToCSSVariables(color.value)
    setIsOpen(false)
  }

  const togglePicker = () => {
    setIsOpen(!isOpen)
  }

  return (
    <div className={cn("relative", className)}>
      {/* Color Picker Button */}
      <button
        onClick={togglePicker}
        className="flex items-center gap-2 px-3 py-2 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/50"
        title="Choose theme color"
      >
        <Palette className="h-4 w-4" />
        <span className="hidden sm:inline text-sm">Theme</span>
      </button>

      {/* Color Picker Dropdown */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />

          {/* Color Grid */}
          <div className="absolute top-full right-0 mt-2 z-50 w-52">
            <ColorSwatchGrid
              colors={BRIGHT_COLORS}
              selectedColor={selectedColor}
              onColorSelect={handleColorSelect}
              swatchSize="md"
              className="shadow-xl"
            />
          </div>
        </>
      )}
    </div>
  )
}

export default ColorPicker
