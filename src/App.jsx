import { useEffect } from "react"
import { IconContext } from "react-icons"

import { ShootingStars } from "@/components/ui/ShootingStars"
import { StarsBackground } from "@/components/ui/StarsBackground"
import { initializeColorTheme } from "@/lib/colors"

import Layout from "./components/Layout/Layout"
import AboutMe from "./pages/AboutMe/AboutMe"
import Contact from "./pages/Contact/Contact"
import Experience from "./pages/Experience/Experience"
import Skills from "./pages/Skills/Skills"
import AITools from "@/pages/AITools/AITools"
import Interests from "@/pages/Interests/Interests"

const App = () => {
  // Ensure page always loads at top and initialize color theme
  useEffect(() => {
    // Disable scroll restoration
    if ("scrollRestoration" in history) {
      history.scrollRestoration = "manual"
    }

    // Force scroll to top on load
    window.scrollTo(0, 0)

    // Initialize color theme from saved preference
    initializeColorTheme()
  }, [])

  return (
    <IconContext.Provider value={{ style: { verticalAlign: "middle" } }}>
      <div className="relative min-h-screen">
        <div className="fixed inset-0 z-0">
          <div className="absolute inset-0 [background:radial-gradient(125%_125%_at_50%_10%,#000_40%,hsl(var(--primary))_100%)]" />
          <ShootingStars />
          <StarsBackground starDensity={0.0008} />
        </div>
        <Layout>
          <div className="relative z-10 pt-4">
            <AboutMe />
            <Skills />
            <AITools />
            <Interests />
            <Experience />
            <Contact />
          </div>
        </Layout>
      </div>
    </IconContext.Provider>
  )
}

export default App
