import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"

import Bo<PERSON><PERSON>ogo from "@/assets/ai/bolt.svg"
import <PERSON><PERSON><PERSON> from "@/assets/ai/claude.svg"
import DeepseekLogo from "@/assets/ai/deepseek.svg"
import Curs<PERSON><PERSON>ogo from "@/assets/ai/cursor.svg"
import OpenWebuiLogo from "@/assets/ai/openwebui.svg"
import Perplexity<PERSON>ogo from "@/assets/ai/perplexity.svg"
import Gemini<PERSON>ogo from "@/assets/ai/gemini.svg"
import LovableLogo from "@/assets/ai/lovable.svg"
import OllamaLogo from "@/assets/ai/ollama.svg"
import OpenAILogo from "@/assets/ai/openai.svg"
import V0Logo from "@/assets/ai/v0.svg"
import WindsurfLogo from "@/assets/ai/windsurf.svg"


import classNames from "classnames"

const AITools = () => {
  const aiTools = [
    { name: "Bo<PERSON>", icon: <PERSON><PERSON><PERSON><PERSON>, color: "#FFF" },
    { name: "<PERSON>", icon: <PERSON><PERSON><PERSON>, color: "#D97757" },
    { name: "Deepseek", icon: DeepseekLogo, color: "#4D6BFE" },
    { name: "Cursor", icon: CursorLogo, color: "#CFCFCF" },
    { name: "Open Web UI", icon: OpenWebuiLogo, color: "#FFF" },
    { name: "Perplexity", icon: PerplexityLogo, color: "#1F808D" },
    { name: "Gemini", icon: GeminiLogo, color: "#1B6AFF" },
    { name: "Lovable", icon: LovableLogo, color: "#916DF0" },
    { name: "Ollama", icon: OllamaLogo, color: "#FFF" },
    { name: "OpenAI", icon: OpenAILogo, color: "#FFF" },
    { name: "V0", icon: V0Logo, color: "#FFF" },
    { name: "Windsurf", icon: WindsurfLogo, color: "#58E5BB" }
  ]

  return (
    <section id="aiTools" className="section">
      <div className="container mx-auto px-8">
        <div className="mb-12 text-center">
          <h2 className="mb-3 text-2xl font-bold text-white md:text-3xl lg:text-4xl">
            My AI Stack
          </h2>

          <p className="text-sm text-muted-foreground md:text-base">
            AI tools and libraries which are my daily driver
          </p>
        </div>

        <div className="grid grid-cols-2 gap-4 px-16 md:grid-cols-3 lg:grid-cols-5 xl:grid-cols-6">
          {aiTools.map((skill) => {
            return (
              <div key={skill.name}>
                <Card className="group relative overflow-hidden border-white/10 bg-white/5 backdrop-blur-sm transition-all duration-300 hover:border-primary/50 hover:bg-white/10 hover:shadow-lg hover:shadow-primary/25">
                  <CardContent
                    className={classNames(
                      "flex flex-col items-center justify-center p-6",
                      skill.wrapperClassName
                    )}
                  >
                    <div>
                      <img
                        src={skill.icon}
                        alt={skill.name}
                        className={classNames(
                          "mb-3 size-8 md:size-10",
                          skill.className
                        )}
                        style={{ color: skill.color }}
                      />
                    </div>
                    <Badge variant="skill" className="text-xs">
                      {skill.name}
                    </Badge>
                  </CardContent>

                  <div className="absolute inset-0 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                    <div
                      className="absolute inset-0 rounded-lg blur-xl"
                      style={{
                        background: `radial-gradient(circle at center, ${skill.color}20 0%, transparent 70%)`
                      }}
                    />
                  </div>
                </Card>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}

export default AITools
