import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"

import <PERSON>jan<PERSON><PERSON><PERSON> from "@/assets/skills/django.svg"
import Aws<PERSON>ogo from "@/assets/skills/aws.svg"
import ReactLogo from "@/assets/skills/react.svg"
import Dock<PERSON><PERSON><PERSON> from "@/assets/skills/docker.svg"
import Git<PERSON>ogo from "@/assets/skills/git.svg"
import Javascript<PERSON>ogo from "@/assets/skills/javascript.svg"
import <PERSON>ra<PERSON>ogo from "@/assets/skills/jira.svg"
import LinuxLogo from "@/assets/skills/linux.svg"
import PostgresqlLogo from "@/assets/skills/postgresql.svg"
import Postman<PERSON>ogo from "@/assets/skills/postman.svg"
import Python<PERSON>ogo from "@/assets/skills/python.svg"
import SassLogo from "@/assets/skills/sass.svg"
import Zustand<PERSON>ogo from "@/assets/skills/zustand.svg"
import Tail<PERSON><PERSON>ogo from "@/assets/skills/tailwindcss.svg"
import <PERSON><PERSON><PERSON>ogo from "@/assets/skills/typescript.svg"
import Vite<PERSON>ogo from "@/assets/skills/vite.svg"
import Zod<PERSON>ogo from "@/assets/skills/zod.svg"
import ReactQueryLogo from "@/assets/skills/react_query.svg"


import classNames from "classnames"

// Utility function to convert hex to rgba
const hexToRgba = (hex, alpha) => {
  const r = parseInt(hex.slice(1, 3), 16)
  const g = parseInt(hex.slice(3, 5), 16)
  const b = parseInt(hex.slice(5, 7), 16)
  return `rgba(${r}, ${g}, ${b}, ${alpha})`
}

const Skills = () => {
  const skills = [
    { name: "React", icon: ReactLogo, color: "#61DAFB" },
    { name: "TypeScript", icon: TypeScriptLogo, color: "#3178C6" },
    { name: "JavaScript", icon: JavascriptLogo, color: "#F7DF1E" },
    { name: "Python", icon: PythonLogo, color: "#3776AB" },
    { name: "Django", icon: DjangoLogo, color: "#092E20" },
    { name: "Docker", icon: DockerLogo, color: "#2496ED" },
    { name: "PostgreSQL", icon: PostgresqlLogo, color: "#336791" },
    { name: "AWS", icon: AwsLogo, color: "#FF9900" },
    { name: "Git", icon: GitLogo, color: "#F05032" },
    { name: "Sass", icon: SassLogo, color: "#CC6699" },
    { name: "Jira", icon: JiraLogo, color: "#0052CC" },
    { name: "Linux", icon: LinuxLogo, color: "#FCC624" },
    { name: "Postman", icon: PostmanLogo, color: "#FF6C37" },
    { name: "Zustand", icon: ZustandLogo, color: "#6E5E6B", className: "!size-16 mb-0", wrapperClassName: "pb-[24px] pt-3" },
    { name: "Tailwind CSS", icon: TailwindLogo, color: "#38BDF8" },
    { name: "Vite", icon: ViteLogo, color: "#FF00FF" },
    { name: "Zod", icon: ZodLogo, color: "#000000" },
    { name: "React Query", icon: ReactQueryLogo, color: "#00435B" },
  ]

  return (
    <section id="skills" className="section">
      <div className="container mx-auto px-8">
        <div className="mb-12 text-center">
          <h2 className="mb-3 text-2xl font-bold text-white md:text-3xl lg:text-4xl">
            Skills & Technologies
          </h2>

          <p className="text-sm text-muted-foreground md:text-base">
            Technologies and tools I work with
          </p>
        </div>

        <div className="grid grid-cols-2 gap-4 px-16 md:grid-cols-3 lg:grid-cols-5 xl:grid-cols-6">
          {skills.map((skill) => {
            return (
              <div key={skill.name}>
                <Card className="group relative overflow-hidden border-white/10 bg-white/5 backdrop-blur-sm transition-all duration-300 hover:border-primary/50 hover:bg-white/10 hover:shadow-lg hover:shadow-primary/25">
                  <CardContent className={(classNames("flex flex-col items-center justify-center p-6", skill.wrapperClassName))}>
                    <div>
                      <img
                        src={skill.icon}
                        alt={skill.name}
                        className={classNames("mb-3 size-8 md:size-10", skill.className)}
                        style={{ color: skill.color }}
                      />
                    </div>
                    <Badge variant="skill" className="text-xs">
                      {skill.name}
                    </Badge>
                  </CardContent>

                  <div className="absolute inset-0 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                    <div
                      className="absolute inset-0 rounded-lg blur-xl"
                      style={{
                        background: `radial-gradient(circle at center, ${hexToRgba(skill.color, 0.2)} 0%, transparent 70%)`
                      }}
                    />
                  </div>
                </Card>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}

export default Skills
